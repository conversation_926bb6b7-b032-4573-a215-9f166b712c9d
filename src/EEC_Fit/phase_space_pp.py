# 导入必要的数学库
import math
import numpy as np


class PhaseSpace:
    """
    用于计算pp产生过程相空间变量的类。

    该类实现了Z玻色子伴随喷注产生过程中各种运动学变量的计算，以及HardQCD
    包括Mandelstam变量、动量分数、部分子能标等。束流能量是实例化时的必需参数。
    参考文献： 1506.01415

    Parameters
    ----------
    Ecom : float, optional
        质心系总能量，单位为GeV，默认为14000 GeV（LHC能量）

    Attributes
    ----------
    Q_BEAM : float
        单束流能量，等于质心系总能量的一半
    MZ : float
        Z玻色子质量，单位为GeV
    GZ : float
        Z玻色子宽度，单位为GeV
    SIN2_EFF : float
        有效弱混合角的正弦平方

    Notes
    -----
    - 所有计算基于树图级QCD过程
    - 使用标准的部分子模型框架
    - 支持不同夸克味道的电弱耦合计算

    Examples
    --------
    >>> ps = PhaseSpaceZJet(Ecom=14000)
    >>> s_val = ps.s()
    >>> print(f"质心系能量平方: {s_val:.2e} GeV²")
    """

    def __init__(self, Ecom: float = 14000):
        """
        初始化相空间计算器。

        Parameters
        ----------
        Ecom : float, optional
            质心系总能量，单位为GeV，默认为14000 GeV

        Notes
        -----
        单束流能量设置为质心系总能量的一半，物理常数设置为实例属性
        """
        self.Q_BEAM = Ecom / 2

        # 物理常数设置为实例属性
        self.MZ = 91.2  # Z玻色子质量 [GeV]
        self.GZ = 0.81  # Z玻色子宽度 [GeV]
        self.SIN2_EFF = 0.23122  # 有效弱混合角的正弦平方

    def s(self) -> float:
        """
        计算强子质心系总能量平方。

        Returns
        -------
        float
            强子质心系能量平方，单位为GeV²

        Notes
        -----
        s = (2 * Q_BEAM)² = 4 * Q_BEAM²
        """
        return 4.0 * self.Q_BEAM**2

    def t(self, pt: float, eta: float) -> float:
        """
        计算Mandelstam变量t。

        Parameters
        ----------
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            Mandelstam变量t，单位为GeV²

        Notes
        -----
        t = -2 * Q_BEAM * (pt * cosh(η) - pt * sinh(η))
        """
        return -2 * self.Q_BEAM * (pt * math.cosh(eta) - pt * math.sinh(eta))

    def u(self, pt: float, eta: float) -> float:
        """
        计算Mandelstam变量u。

        Parameters
        ----------
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            Mandelstam变量u，单位为GeV²

        Notes
        -----
        u = -2 * Q_BEAM * (pt * cosh(η) + pt * sinh(η))
        """
        return -2 * self.Q_BEAM * (pt * math.cosh(eta) + pt * math.sinh(eta))

    def v_var(self, pt: float, eta: float) -> float:
        """
        计算无量纲变量V。

        Parameters
        ----------
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            无量纲变量V

        Notes
        -----
        V = 1 - pt / sqrt(s) * exp(-eta)，用于参数化相空间
        """
        return 1 - pt / np.sqrt(self.s()) * np.exp(-eta)

    def w_var(self, pt: float, eta: float) -> float:
        """
        计算无量纲变量W。

        Parameters
        ----------
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            无量纲变量W

        Notes
        W = pt^2 / s / V / (1 - V)，用于参数化相空间
        -----
        """
        return pt**2 / self.s() / self.v_var(pt, eta) / (1 - self.v_var(pt, eta))

    def x1min(self, pt: float, eta: float) -> float:
        """
        计算动量分数x1的最小值。

        Parameters
        ----------
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            第一个质子的动量分数x1的最小值

        Notes
        -----
        x1表示第一个质子中参与硬散射的部分子携带的动量分数
        """
        return self.w_var(pt, eta)

    def x2min(self, x1: float, pt: float, eta: float) -> float:
        """
        计算动量分数x2的最小值。

        Parameters
        ----------
        x1 : float
            第一个质子的动量分数x1
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            第二个质子的动量分数x2的最小值

        Notes
        -----
        x2表示第二个质子中参与硬散射的部分子携带的动量分数
        """
        # w参数在此公式中未使用，但保留用于接口一致性
        return 1 - self.v_var(pt, eta) / (
            1 - self.v_var(pt, eta) * self.w_var(pt, eta) / x1
        )

    def pthat(self, zc: float, pt: float) -> float:
        """
        计算部分子动量。

        Parameters
        ----------
        zc : float
            能量分数参数
        pt : float
            横动量，单位为GeV

        Returns
        -------
        float
            部分子动量p̂T，单位为GeV

        Notes
        -----
        p̂T = pt / zc
        """
        return pt / zc

    def etahat(self, x1: float, x2: float, eta: float) -> float:
        """
        计算部分子赝快度。

        Parameters
        ----------
        x1 : float
            第一个质子的动量分数x1
        x2 : float
            第二个质子的动量分数x2
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            部分子赝快度,考虑的是能量相等的部分子坐标系。

        Notes
        -----
        部分子赝快度定义为η̂ = eta - 0.5 * ln(x1 / x2)
        """
        return eta - 0.5 * np.log(x1 / x2)

    def shat(self, x1: float, x2: float) -> float:
        """
        计算部分子质心系能量平方。

        Parameters
        ----------
        x1 : float
            第一个质子的动量分数x1

        x2 : float
            第二个质子的动量分数x2

        Returns
        -------
        float
            部分子质心系能量平方ŝ，单位为GeV²

        Notes
        -----
        ŝ = x1 * x2 * s，表示参与硬散射的部分子的不变质量平方
        """
        return x1 * x2 * self.s()

    def that(self, zc: float, x1: float, x2: float, pt: float, eta: float) -> float:
        """
        计算部分子Mandelstam变量t̂。

        Parameters
        ----------
        zc : float
            能量分数参数
        x1 : float
            第一个质子的动量分数x1
        x2 : float
            第二个质子的动量分数x2
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            部分子Mandelstam变量t̂，单位为GeV²

        Notes
        -----
        t̂ = x1 * t / zc，表示部分子级别的动量转移
        """
        return (
            -np.sqrt(self.shat(x1, x2))
            * self.pthat(zc, pt)
            * np.exp(-self.etahat(x1, x2, eta))
        )

    def uhat(self, zc: float, x1: float, x2: float, pt: float, eta: float) -> float:
        """
        计算部分子Mandelstam变量û。

        Parameters
        ----------
        zc : float
            能量分数参数
        x1 : float
            第一个质子的动量分数x1
        x2 : float
            第二个质子的动量分数x2
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
            部分子Mandelstam变量û，单位为GeV²

        Notes
        -----
        û = x2 * u / zc，表示部分子级别的动量转移
        """
        return (
            -np.sqrt(self.shat(x1, x2))
            * self.pthat(zc, pt)
            * np.sinh(self.etahat(x1, x2, eta))
        )

    def vq_plus_aq(self, quark_flavor: int) -> float:
        """
        计算电弱耦合常数的平方和（与束流能量无关）。

        Parameters
        ----------
        quark_flavor : int
            夸克味道编号：
            - 1, 3, 5: d, s, b夸克（下型夸克）
            - 2, 4: u, c夸克（上型夸克）
            - 6: t夸克（不支持）

        Returns
        -------
        float
            电弱耦合常数的平方和 v_q² + a_q²

        Raises
        ------
        ValueError
            当夸克味道为6（top夸克）或未知值时抛出异常

        Notes
        -----
        - v_q和a_q分别是矢量和轴矢量耦合常数
        - 计算基于标准模型的电弱理论
        - 使用有效弱混合角sin²θ_eff
        """
        if quark_flavor in [1, 3, 5]:  # d, s, b夸克（下型夸克）
            term1 = -0.5 - (2.0 / 3.0) * self.SIN2_EFF
            return term1**2 + (-0.5) ** 2
        elif quark_flavor in [2, 4]:  # u, c夸克（上型夸克）
            term1 = 0.5 + (4.0 / 3.0) * self.SIN2_EFF
            return term1**2 + (0.5) ** 2
        elif quark_flavor == 6:
            raise ValueError("无效参数：不支持top夸克")
        else:
            raise ValueError(f"未知的夸克味道：{quark_flavor}")


def run_demonstration() -> None:
    """
    运行PhaseSpace类的演示示例。

    展示如何使用PhaseSpace类进行pp相空间变量计算，包括：
    1. 创建不同对撞机能量的实例
    2. 计算基本运动学变量
    3. 计算电弱耦合常数
    4. 演示完整的相空间计算流程
    """
    print("\n" + "=" * 60)
    print("PhaseSpace 类使用示例")
    print("=" * 60)

    # 示例1：LHC能量下的计算
    print("\n[示例1: LHC能量下的相空间计算]")
    ps_lhc = PhaseSpace(Ecom=14000)
    print(f"  -> LHC参数 (Q_beam = {ps_lhc.Q_BEAM:.1f} GeV)")

    s_val_lhc = ps_lhc.s()
    print(f"  -> 质心系能量平方 s = {s_val_lhc:.4e} GeV²")


if __name__ == "__main__":
    run_demonstration()
